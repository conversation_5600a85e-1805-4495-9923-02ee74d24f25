import {
  ListStaffDocument,
  useAdminRemoveUserMutation,
  useListStaffQuery,
  User,
  UserRole,
} from '@/generated/graphql';
import { useCallback, useMemo, useState } from 'react';
import EditDashboardUserModal from '../profile-info/components/edit-dashboard-user-modal/EditDashboardUserModal';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { ApolloError, useApolloClient } from '@apollo/client';
import { useToast } from '@/hooks/useToast';
import RemoveUserModal from '@/components/modals/confirm-modal/RemoveUserModal';
import UserList from './components/UserList';

export const Permission = () => {
  const [editUser, setEditUser] = useState<User | null>(null);
  const { data, loading } = useListStaffQuery();
  const { userData } = useAuthContext();
  const { toast } = useToast();
  const client = useApolloClient();
  const isAdminUser = userData?.role === UserRole.Admin;
  const [isRemoveUserModalOpen, setIsRemoveUserModalOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);

  const [removeUserMutation, { loading: isRemovingUser }] = useAdminRemoveUserMutation();

  const allUsers = useMemo(() => data?.listStaff ?? [], [data?.listStaff]);

  const currentUser = useMemo(
    () => allUsers.find((user) => user.id === userData?.id),
    [allUsers, userData?.id]
  );

  const userList = useMemo(
    () => allUsers.filter((user) => user.id !== userData?.id),
    [allUsers, userData?.id]
  );

  const adminUsers = useMemo(() => {
    if (loading) {
      return Array(3).fill({});
    }

    const admins = userList.filter((user) => user.role === UserRole.Admin) ?? [];

    // Add current user at the beginning if admin
    if (currentUser && currentUser.role === UserRole.Admin) {
      return [currentUser, ...admins];
    }

    return admins;
  }, [userList, loading, currentUser]);

  const editorUsers = useMemo(() => {
    if (loading) {
      return Array(3).fill({});
    }

    const editors = userList.filter((user) => user.role === UserRole.Editor) ?? [];

    // Add current user at the beginning if editor
    if (currentUser && currentUser.role === UserRole.Editor) {
      return [currentUser, ...editors];
    }

    return editors;
  }, [userList, loading, currentUser]);

  const shouldShowActions = useCallback(
    (user: User) => isAdminUser && user.id !== userData?.id,
    [isAdminUser, userData?.id]
  );

  const removeUser = useCallback(
    async (userId: string) => {
      try {
        const res = await removeUserMutation({ variables: { input: { userId } } });
        client.refetchQueries({ include: [ListStaffDocument] });
        setIsRemoveUserModalOpen(false);

        toast({
          variant: 'success',
          title: res.data?.adminRemoveUser.message,
        });
      } catch (error) {
        setIsRemoveUserModalOpen(false);

        if (error instanceof ApolloError) {
          toast({
            variant: 'destructive',
            title: error.graphQLErrors[0].message,
          });
        }
      }
    },
    [client, removeUserMutation, toast]
  );

  const handleEditUser = (user: User) => {
    setEditUser(user);
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUserId(userId);
    setIsRemoveUserModalOpen(true);
  };

  return (
    <div className='rounded-xl shadow-sm w-full gap-8 flex flex-col'>
      <UserList
        users={adminUsers}
        isLoading={loading}
        role={UserRole.Admin}
        showActions={shouldShowActions}
        onRemoveUser={handleRemoveUser}
        onEditUser={handleEditUser}
      />
      <UserList
        users={editorUsers}
        isLoading={loading}
        role={UserRole.Editor}
        showActions={shouldShowActions}
        onRemoveUser={handleRemoveUser}
        onEditUser={handleEditUser}
      />
      {editUser && (
        <EditDashboardUserModal
          open={!!editUser}
          onOpenChange={() => setEditUser(null)}
          onCancel={() => setEditUser(null)}
          user={editUser}
        />
      )}
      <RemoveUserModal
        isOpen={isRemoveUserModalOpen}
        onOpenChange={setIsRemoveUserModalOpen}
        onCancel={() => setIsRemoveUserModalOpen(false)}
        onConfirm={() => {
          removeUser(selectedUserId as string);
        }}
        isLoading={isRemovingUser}
        title='Remove User'
        description='Are you sure you want to remove this user? This action cannot be undone.'
      />
    </div>
  );
};
