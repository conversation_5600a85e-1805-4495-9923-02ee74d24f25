import { Checkbox } from '@/components/ui/Checkbox';
import ActionCell from '@/components/ui/table/ActionCell';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { ClubCategoryEnum } from '@/generated/graphql';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';

interface AssociationClubColumnsProps {
  isLoading: boolean;
  showActions: boolean;
  selectedRowId: string | null;
  actionCellRef?: React.RefObject<HTMLDivElement>;
  associationId: string;
  onNavigate: (clubId: string) => void;
  setSelectedRowId: (id: string | null) => void;
}

export function generateAssociationClubColumns({
  isLoading,
  showActions,
  actionCellRef,
  selectedRowId,
  associationId,
  onNavigate,
  setSelectedRowId,
}: AssociationClubColumnsProps) {
  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'NAME',
      header: ({ column }) => <HeaderColumn column={column}>Club Name</HeaderColumn>,
      cell: ({ row }) => {
        const club = row.original;
        const clubName = club?.clubTemplate?.name || 'Unnamed Club';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span
                className='truncate text-gray-900 font-medium cursor-pointer hover:text-primary hover:underline'
                onClick={(e) => {
                  e.stopPropagation();
                  onNavigate(club.id);
                }}
              >
                {clubName}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'category',
      header: ({ column }) => <HeaderColumn column={column}>Club Category</HeaderColumn>,
      cell: ({ row }) => {
        const category = row.original?.clubTemplate?.category;
        const getCategoryLabel = (category: ClubCategoryEnum | null | undefined) => {
          switch (category) {
            case ClubCategoryEnum.Creative:
              return 'Creative';
            case ClubCategoryEnum.FitnessOutdoor:
              return 'Fitness & Outdoor';
            case ClubCategoryEnum.FoodDrink:
              return 'Food & Drink';
            case ClubCategoryEnum.Hobbies:
              return 'Hobbies';
            case ClubCategoryEnum.SocialFamily:
              return 'Social & Family';
            default:
              return 'N/A';
          }
        };

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {getCategoryLabel(category)}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 150,
      size: 180,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => <HeaderColumn column={column}>Description</HeaderColumn>,
      cell: ({ row }) => {
        const description = row.original?.clubTemplate?.description;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>{description}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 200,
      size: 300,
    },
    {
      accessorKey: 'memberCount',
      header: ({ column }) => <HeaderColumn column={column}>Total Members</HeaderColumn>,
      cell: ({ row }) => {
        const memberCount = row.original?.memberCount || 0;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>{memberCount}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      enableSorting: false,
    },
    {
      accessorKey: 'UPDATED_AT',
      header: ({ column }) => <HeaderColumn column={column}>Last Activity</HeaderColumn>,
      cell: ({ row }) => {
        const lastActivity = '08/10/2024'; // This should come from API
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>{lastActivity}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
  ];

  if (showActions) {
    columns.push({
      accessorKey: 'actions',
      header: '',
      cell: ({ row }) => {
        const club = row.original;
        const isSelected = selectedRowId === row.id;

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div
              ref={actionCellRef}
              className={cn('flex justify-center', {
                'bg-blue-50': isSelected,
              })}
            >
              <ActionCell
                isSelected={isSelected}
                setSelectedRowId={setSelectedRowId}
                actions={[
                  {
                    label: 'View Club Details',
                    onClick: () => onNavigate(club.id),
                  },
                ]}
              />
            </div>
          </SkeletonCell>
        );
      },
      minSize: 80,
      maxSize: 80,
      meta: {
        padding: '20px 16px',
      },
    });
  }

  return columns;
}
