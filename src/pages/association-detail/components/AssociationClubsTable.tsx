import { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import { generateAssociationClubColumns } from './AssociationClubColumns';
import { ColumnFiltersState, RowSelectionState, Row } from '@tanstack/react-table';
import usePagination from '@/hooks/usePagination';
import useSort from '@/hooks/useSort';
import {
  ClubCategoryEnum,
  OrderDirection,
  ClubOrderByEnum,
  useAdminClubsQuery,
} from '@/generated/graphql';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import TableData from '@/components/ui/table/TableData';
import { useNavigate } from 'react-router-dom';
import { AppRoutePaths } from '@/lib/constants';
import AssociationTableFilter from '@/pages/associations/components/AssociationTableFilter';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';

interface AssociationClubsTableProps {
  associationId: string;
}

const AssociationClubsTable = ({ associationId }: AssociationClubsTableProps) => {
  const { isLoading: isLoadingAuth } = useAuthContext();
  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort();
  const { searchTemp, setSearchTemp } = useSearchQuery('association-clubs');
  const navigate = useNavigate();

  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const actionCellRef = useRef<HTMLDivElement>(null);

  const [selectedCategory] = useState<string>('All Categories');

  // Reset sorting when component mounts
  useEffect(() => {
    setSorting([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset pagination when filters changes
  useEffect(() => {
    if (selectedCategory !== 'All Categories') {
      setPagination({ pageIndex: 1, pageSize: 10 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCategory]);

  const { data: clubs, loading: isLoadingClubs } = useAdminClubsQuery({
    variables: {
      associationId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp.trim() || undefined,
        category:
          selectedCategory !== 'All Categories'
            ? (selectedCategory as ClubCategoryEnum)
            : undefined,
      },
      orderBy:
        sorting.length > 0
          ? {
              field: sorting[0]?.id as ClubOrderByEnum,
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
  });

  const clubsData = useMemo(() => {
    if (isLoadingClubs || isLoadingAuth) {
      return Array(10).fill({});
    }
    return clubs?.adminClubs?.items ?? [];
  }, [isLoadingClubs, isLoadingAuth, clubs]);

  const totalClubs = useMemo(() => clubs?.adminClubs?.total ?? 0, [clubs]);

  const handleRowClick = useCallback(
    (row: Row<any>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        actionCellRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start',
        });
      }
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateAssociationClubColumns({
        isLoading: isLoadingClubs,
        showActions: true,
        selectedRowId,
        associationId,
        onNavigate: (clubId: string) =>
          navigate(`${AppRoutePaths.ASSOCIATIONS}/${associationId}/clubs/${clubId}`),
        setSelectedRowId,
        actionCellRef,
      }),
    [isLoadingClubs, selectedRowId, navigate, associationId]
  );

  return (
    <div className='w-full flex flex-col sm:py-8 sm:pb-12 flex-1 rounded-lg'>
      {/* Search and Filters */}
      <AssociationTableFilter search={searchTemp} setSearch={setSearchTemp} />

      <div className='w-full border rounded-lg overflow-auto'>
        {/* Table Header */}
        <TableHeaderCount title='Clubs' total={totalClubs} />

        {/* Table */}
        <TableData
          columns={columns}
          data={clubsData}
          pagination={pagination}
          sorting={sorting}
          filters={columnFilters}
          onColumnFiltersChange={setColumnFilters}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        <TablePagination
          pageCount={Math.ceil((clubs?.adminClubs?.total || 0) / pagination.pageSize)}
          currentPage={pagination.pageIndex - 1}
          onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
        />
      </div>
    </div>
  );
};

export default AssociationClubsTable;
