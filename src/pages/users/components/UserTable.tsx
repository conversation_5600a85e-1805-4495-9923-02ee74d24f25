import TableData from '@/components/ui/table/TableData';
import { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import generateUserColumns from './UserColumns';
import { ColumnFiltersState, RowSelectionState, Row } from '@tanstack/react-table';
import usePagination from '@/hooks/usePagination';
import UserTableFilter from './UserTableFilter';
import useSort from '@/hooks/useSort';
import {
  Association,
  OrderDirection,
  UserSortField,
  useUsersQuery,
  UserRole,
  User,
} from '@/generated/graphql';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { DATE_FORMAT_API } from '@/lib/constants';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { useUserActions } from '../hooks/useUserActions';
import { UserModals } from './UserModals';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';

const UserTable = () => {
  const { userData, isLoading: isLoadingAuth } = useAuthContext();
  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort();
  const { searchTemp, setSearchTemp } = useSearchQuery('users');

  const userActions = useUserActions();
  const {
    selectedUser,
    togglingUserId,
    handleSendSignupEmails,
    handleOpenRemoveUserModal,
    handleEditUser,
    handleOpenResetPasswordModal,
    handleOpenUserSalesforce,
    handleToggleUserClubAccess,
  } = userActions;

  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const actionCellRef = useRef<HTMLDivElement>(null);

  const [selectedHOA, setSelectedHOA] = useState<Association | null>(null);
  const [date, setDate] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined,
  });

  // Reset sorting when component mounts
  useEffect(() => {
    setSorting([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset pagination when filters changes
  useEffect(() => {
    if (
      selectedHOA !== null ||
      date?.from !== undefined ||
      date?.to !== undefined ||
      searchTemp !== ''
    ) {
      setPagination({ pageIndex: 1, pageSize: 10 });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedHOA, date, searchTemp]);

  const { data: users, loading: isLoadingUsers } = useUsersQuery({
    variables: {
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      search: searchTemp.trim(),
      sort:
        sorting.length > 0
          ? {
              field: sorting[0]?.id as UserSortField,
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
      filter: {
        dateJoinedFrom: date?.from ? format(date?.from, DATE_FORMAT_API) : undefined,
        dateJoinedTo: date?.to ? format(date?.to, DATE_FORMAT_API) : undefined,
        associationId: selectedHOA?.id,
      },
    },
  });

  const usersData = useMemo(() => {
    if (isLoadingUsers || isLoadingAuth) {
      return Array(10).fill({});
    }
    return users?.users.items ?? [];
  }, [isLoadingUsers, isLoadingAuth, users]);

  const totalUsers = useMemo(() => users?.users.total ?? 0, [users]);

  const handleRowClick = useCallback(
    (row: Row<User>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);

      if (newSelectedId && actionCellRef.current) {
        actionCellRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'start',
        });
      }
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateUserColumns({
        isLoading: isLoadingUsers,
        onSendSignupEmails: handleSendSignupEmails,
        onRemoveUser: handleOpenRemoveUserModal,
        onEditUser: handleEditUser,
        onResetPassword: handleOpenResetPasswordModal,
        onOpenUserSalesforce: handleOpenUserSalesforce,
        onToggleUserClubAccess: handleToggleUserClubAccess,
        togglingUserId,
        showActions: userData?.role === UserRole.Admin,
        selectedRowId,
        setSelectedRowId,
        actionCellRef,
      }),
    [
      isLoadingUsers,
      handleSendSignupEmails,
      handleOpenRemoveUserModal,
      handleEditUser,
      handleOpenResetPasswordModal,
      handleOpenUserSalesforce,
      handleToggleUserClubAccess,
      togglingUserId,
      userData?.role,
      selectedRowId,
    ]
  );

  return (
    <div className='w-full flex flex-col sm:p-8 sm:pb-12 flex-1 rounded-lg'>
      <UserTableFilter
        search={searchTemp}
        setSearch={setSearchTemp}
        selectedHOA={selectedHOA}
        setSelectedHOA={setSelectedHOA}
        date={date}
        setDate={setDate}
      />
      <div className='w-full border rounded-lg overflow-auto'>
        <TableHeaderCount title='Users' total={totalUsers} />
        <TableData
          columns={columns}
          data={usersData}
          pagination={pagination}
          sorting={sorting}
          filters={columnFilters}
          onColumnFiltersChange={setColumnFilters}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        <TablePagination
          pageCount={Math.ceil((users?.users.total || 0) / pagination.pageSize)}
          currentPage={pagination.pageIndex - 1}
          onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
        />
      </div>
      <UserModals userActions={userActions} selectedUser={selectedUser} />
    </div>
  );
};

export default UserTable;
