import { ClubTemplate } from '@/generated/graphql';
import RemoveUserModal from '@/components/modals/confirm-modal/RemoveUserModal';
import { useClubActions } from '../../hooks/useClubActions';

interface ClubModalProps {
  clubActions: ReturnType<typeof useClubActions>;
  selectedClub: ClubTemplate | null;
}

export const DeleteClubModal = ({ clubActions, selectedClub }: ClubModalProps) => {
  const {
    isDeleteClubModalOpen,
    setIsDeleteClubModalOpen,
    isDeletingClub,
    handleConfirmDeleteClub,
  } = clubActions;

  return (
    <>
      {/* Delete Club Modal */}
      <RemoveUserModal
        isOpen={isDeleteClubModalOpen}
        onOpenChange={setIsDeleteClubModalOpen}
        onCancel={() => setIsDeleteClubModalOpen(false)}
        onConfirm={() => {
          if (selectedClub) {
            handleConfirmDeleteClub(selectedClub.id);
          }
        }}
        isLoading={isDeletingClub}
        title='Delete Club'
        description='Are you sure you want to delete this club? This action cannot be undone.'
      />
    </>
  );
};
