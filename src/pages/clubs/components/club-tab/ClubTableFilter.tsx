import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { ChevronDown, ListFilter, X, Check } from 'lucide-react';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';
import { ClubCategoryEnum } from '@/generated/graphql';
import { Command, CommandEmpty, CommandGroup, CommandItem } from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import DateRangePicker from '@/components/date-range-picker/DateRangePicker';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/lib/constants';
import { useIsMobile } from '@/hooks/use-mobile';

interface CategorySelectorProps {
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  tempCategorySearch: string;
  setTempCategorySearch: (value: string) => void;
}

const CATEGORIES = [
  { value: ClubCategoryEnum.Creative, label: 'Creative' },
  { value: ClubCategoryEnum.FitnessOutdoor, label: 'Fitness & Outdoor' },
  { value: ClubCategoryEnum.FoodDrink, label: 'Food & Drink' },
  { value: ClubCategoryEnum.Hobbies, label: 'Hobbies' },
  { value: ClubCategoryEnum.SocialFamily, label: 'Social & Family' },
];

const CategorySelector = ({
  selectedCategory,
  setSelectedCategory,
  tempCategorySearch,
}: CategorySelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const filteredCategories = CATEGORIES.filter((category) =>
    category.label.toLowerCase().includes(tempCategorySearch.toLowerCase())
  );

  const displayedCategory = CATEGORIES.find((cat) => cat.value === selectedCategory);

  return (
    <Popover modal open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={isOpen}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            {
              'text-muted-foreground': !selectedCategory || selectedCategory === 'All Categories',
            }
          )}
        >
          {displayedCategory ? displayedCategory.label : 'Club Category'}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(event) => event.preventDefault()}
      >
        <Command shouldFilter={false}>
          <CommandEmpty>No category found.</CommandEmpty>
          <CommandGroup>
            <ScrollArea className='max-h-64 !overflow-y-auto'>
              {filteredCategories.map((category) => (
                <CommandItem
                  key={category.value}
                  value={category.label}
                  onSelect={() => {
                    setSelectedCategory(category.value);
                    setIsOpen(false);
                  }}
                  className='flex items-center justify-between'
                >
                  <span>{category.label}</span>
                  {category.value === selectedCategory && (
                    <Check className='h-4 w-4 text-primary' />
                  )}
                </CommandItem>
              ))}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

interface ClubTableFilterProps {
  search: string;
  setSearch: (search: string) => void;
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
  lastUpdated: DateRange | undefined;
  setLastUpdated: (date: DateRange | undefined) => void;
}

const ClubTableFilter = ({
  search,
  setSearch,
  selectedCategory,
  setSelectedCategory,
  lastUpdated,
  setLastUpdated,
}: ClubTableFilterProps) => {
  const [isOpenFilters, setIsOpenFilters] = useState(false);
  const [tempSelectedCategory, setTempSelectedCategory] = useState(selectedCategory);
  const [tempCategorySearch, setTempCategorySearch] = useState('');
  const [tempLastUpdated, setTempLastUpdated] = useState<DateRange | undefined>(lastUpdated);
  const [isDateOpen, setIsDateOpen] = useState(false);

  const isMobile = useIsMobile();

  const handleApplyFilters = () => {
    setSelectedCategory(tempSelectedCategory);
    setIsOpenFilters(false);
    setLastUpdated(tempLastUpdated);
  };

  const handleCancelFilters = () => {
    setIsOpenFilters(false);
    setTempSelectedCategory(selectedCategory);
    setTempCategorySearch('');
    setTempLastUpdated(lastUpdated);
  };

  const onClearAll = () => {
    setSelectedCategory('All Categories');
    setTempSelectedCategory('All Categories');
    setTempCategorySearch('');
    setIsOpenFilters(false);
    setLastUpdated(undefined);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);
    if (!open) {
      setTempSelectedCategory(selectedCategory);
      setTempCategorySearch('');
    }
  };

  return (
    <div className='pb-4'>
      <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4'>
        <div className='flex w-full items-center gap-2'>
          <SearchInput name='search' onChange={(search) => setSearch(search)} value={search} />
        </div>
        <div className='flex w-full sm:w-auto items-center gap-2'>
          <Popover open={isOpenFilters} onOpenChange={handleFilterOpenChange}>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className='gap-2 w-full text-gray-700 font-semibold sm:w-auto'
              >
                <ListFilter className='w-5 h-5' />
                Filters
              </Button>
            </PopoverTrigger>
            <PopoverContent align='end' className='sm:w-80 w-[--radix-popover-trigger-width]'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between mb-2'>
                  <label className='text-sm font-medium'>Filter by</label>
                  {selectedCategory !== 'All Categories' && (
                    <span className='cursor-pointer text-sm font-medium' onClick={onClearAll}>
                      Clear
                    </span>
                  )}
                </div>

                {/* Club Category Filter */}
                <div className='space-y-2'>
                  <CategorySelector
                    selectedCategory={tempSelectedCategory}
                    setSelectedCategory={setTempSelectedCategory}
                    tempCategorySearch={tempCategorySearch}
                    setTempCategorySearch={setTempCategorySearch}
                  />
                </div>

                <div className='space-y-2'>
                  <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant='outline'
                        className={cn(
                          'w-full flex justify-between items-center gap-2 font-normal text-[#191E3B] p-3 border border-gray-200 rounded-md text-sm bg-white',
                          {
                            'text-muted-foreground': !tempLastUpdated?.from,
                          }
                        )}
                      >
                        {tempLastUpdated?.from ? (
                          tempLastUpdated.to ? (
                            <>
                              {format(tempLastUpdated.from, DATE_FORMAT)} -{' '}
                              {format(tempLastUpdated.to, DATE_FORMAT)}
                            </>
                          ) : (
                            format(tempLastUpdated.from, DATE_FORMAT)
                          )
                        ) : (
                          <span>Last Updated</span>
                        )}
                        <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className='w-[--radix-popover-trigger-width] items-center sm:w-auto p-0'
                      align='end'
                      sideOffset={isMobile ? -100 : 5}
                    >
                      <DateRangePicker
                        date={tempLastUpdated}
                        setDate={setTempLastUpdated}
                        onConfirm={(date) => {
                          setTempLastUpdated(date);
                          setIsDateOpen(false);
                        }}
                        onCancel={() => {
                          setIsDateOpen(false);
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className='flex w-full items-center gap-2'>
                  <Button onClick={handleCancelFilters} variant='outline' className='w-full'>
                    Cancel
                  </Button>
                  <Button
                    disabled={false}
                    onClick={handleApplyFilters}
                    variant='default'
                    className='w-full'
                  >
                    Confirm
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
      <div className='flex flex-col sm:flex-row gap-2'>
        {selectedCategory !== 'All Categories' && (
          <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
            {CATEGORIES.find((cat) => cat.value === selectedCategory)?.label || selectedCategory}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setSelectedCategory('All Categories');
                setTempSelectedCategory('All Categories');
              }}
            />
          </Badge>
        )}
        {lastUpdated && (lastUpdated.from || lastUpdated.to) && (
          <Badge className='text-sm gap-2 text-primary hover:bg-secondary w-fit bg-secondary font-medium'>
            {lastUpdated.from && format(lastUpdated.from, DATE_FORMAT)}
            {lastUpdated.to && ` - ${format(lastUpdated.to, DATE_FORMAT)}`}
            <X
              className='w-4 h-4 cursor-pointer'
              onClick={() => {
                setLastUpdated(undefined);
                setTempLastUpdated(undefined);
              }}
            />
          </Badge>
        )}
      </div>
    </div>
  );
};

export default ClubTableFilter;
