import { ClubCategoryEnum } from '@/generated/graphql';

export function getCategoryLabel(category: ClubCategoryEnum | null | undefined) {
  switch (category) {
    case ClubCategoryEnum.Creative:
      return 'Creative';
    case ClubCategoryEnum.FitnessOutdoor:
      return 'Fitness & Outdoor';
    case ClubCategoryEnum.FoodDrink:
      return 'Food & Drink';
    case ClubCategoryEnum.Hobbies:
      return 'Hobbies';
    case ClubCategoryEnum.SocialFamily:
      return 'Social & Family';
    default:
      return 'N/A';
  }
}

export function getUserName(firstName?: string | null, lastName?: string | null) {
  if (firstName && lastName) return `${firstName} ${lastName}`;
  if (firstName) return firstName;
  if (lastName) return lastName;
  return 'Unknown User';
}
