import { useState } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Command, CommandGroup, CommandItem } from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';

interface StatusSelectorProps {
  selectedStatus: string;
  setSelectedStatus: (status: string) => void;
}

export function StatusSelector({ selectedStatus, setSelectedStatus }: StatusSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const statuses = [
    { value: 'Approved', label: 'Approved' },
    { value: 'Rejected', label: 'Rejected' },
  ];

  const selectedStatusLabel = statuses.find((status) => status.value === selectedStatus)?.label;

  return (
    <Popover modal={false} open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={isOpen}
          className='w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white'
        >
          {selectedStatusLabel || 'Request Sent'}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[--radix-popover-trigger-width] p-0'>
        <Command>
          <CommandGroup>
            <ScrollArea className='max-h-64'>
              {statuses.map((status) => (
                <CommandItem
                  key={status.value}
                  value={status.label}
                  onSelect={() => {
                    setSelectedStatus(status.value);
                    setIsOpen(false);
                  }}
                  className='flex items-center justify-between'
                >
                  <span>{status.label}</span>
                  {status.value === selectedStatus && <Check className='h-4 w-4 text-primary' />}
                </CommandItem>
              ))}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
