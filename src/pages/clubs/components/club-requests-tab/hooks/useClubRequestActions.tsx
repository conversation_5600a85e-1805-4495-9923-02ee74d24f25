import { useCallback } from 'react';
import {
  useUpdateClubRequestMutation,
  ClubRequestStatus,
  ClubRequestsDocument,
} from '@/generated/graphql';
import { toast } from '@/hooks/useToast';
import { ApolloError } from '@apollo/client';

export function useClubRequestActions() {
  const [updateClubRequest] = useUpdateClubRequestMutation();

  const handleApiError = useCallback((error: unknown, errorMessage = 'Operation failed') => {
    if (error instanceof ApolloError) {
      toast({
        variant: 'destructive',
        title: error.graphQLErrors[0]?.message || errorMessage,
      });
    }
  }, []);

  // Action handlers
  const handleApproveRequest = useCallback(
    async (requestId: string) => {
      try {
        await updateClubRequest({
          variables: {
            input: {
              id: requestId,
              status: ClubRequestStatus.Approved,
            },
          },
          refetchQueries: [ClubRequestsDocument],
        });
      } catch (error) {
        handleApiError(error, 'Failed to approve request. Please try again.');
      }
    },
    [handleApiError, updateClubRequest]
  );

  const handleDeclineRequest = useCallback(
    async (requestId: string) => {
      try {
        await updateClubRequest({
          variables: {
            input: {
              id: requestId,
              status: ClubRequestStatus.Rejected,
            },
          },
        });
      } catch (error) {
        handleApiError(error, 'Failed to decline request');
      }
    },
    [updateClubRequest, handleApiError]
  );

  return {
    // Actions
    handleApproveRequest,
    handleDeclineRequest,
  };
}
