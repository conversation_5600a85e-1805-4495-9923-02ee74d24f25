import { useState } from 'react';

export interface ClubRequestFilters {
  // Applied filter states
  selectedCategory: string;
  selectedStatus: string;
  selectedUserId: string | null;
  selectedUserName: string | null;
  isOpenFilters: boolean;

  // Temporary filter states
  tempSelectedCategory: string;
  tempSelectedStatus: string;
  tempSelectedUserId: string | null;
  tempSelectedUserName: string | null;
  tempCategorySearch: string;
  tempUserSearch: string;

  // Actions
  setSelectedCategory: (category: string) => void;
  setSelectedStatus: (status: string) => void;
  setSelectedUserId: (userId: string | null) => void;
  setSelectedUserName: (userEmail: string | null) => void;
  setTempSelectedCategory: (category: string) => void;
  setTempSelectedStatus: (status: string) => void;
  setTempSelectedUserId: (userId: string | null) => void;
  setTempSelectedUserName: (userEmail: string | null) => void;
  setTempCategorySearch: (search: string) => void;
  setTempUserSearch: (search: string) => void;
  handleApplyFilters: () => void;
  handleCancelFilters: () => void;
  onClearAll: () => void;
  handleFilterOpenChange: (open: boolean) => void;
}

export function useClubRequestFilters(): ClubRequestFilters {
  // Applied filter states
  const [selectedCategory, setSelectedCategory] = useState<string>('All Categories');
  const [selectedStatus, setSelectedStatus] = useState<string>('All Status');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedUserName, setSelectedUserName] = useState<string | null>(null);

  // Filter popover state
  const [isOpenFilters, setIsOpenFilters] = useState(false);

  // Temporary filter states (used while the filter modal is open)
  const [tempSelectedCategory, setTempSelectedCategory] = useState(selectedCategory);
  const [tempSelectedStatus, setTempSelectedStatus] = useState(selectedStatus);
  const [tempSelectedUserId, setTempSelectedUserId] = useState(selectedUserId);
  const [tempSelectedUserName, setTempSelectedUserName] = useState(selectedUserName);
  const [tempCategorySearch, setTempCategorySearch] = useState('');
  const [tempUserSearch, setTempUserSearch] = useState('');

  // Filter management functions
  const handleApplyFilters = () => {
    // Apply temporary filters to actual filter state
    setSelectedCategory(tempSelectedCategory);
    setSelectedStatus(tempSelectedStatus);
    setSelectedUserId(tempSelectedUserId);
    setSelectedUserName(tempSelectedUserName);

    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    // Close modal and reset temporary states to current applied filters
    setIsOpenFilters(false);
    setTempSelectedCategory(selectedCategory);
    setTempSelectedStatus(selectedStatus);
    setTempSelectedUserId(selectedUserId);
    setTempSelectedUserName(selectedUserName);
    setTempCategorySearch('');
    setTempUserSearch('');
  };

  const onClearAll = () => {
    // Reset all filters to default values
    const defaultCategory = 'All Categories';
    const defaultStatus = 'All Status';
    const defaultUserId = null;
    const defaultUserEmail = null;

    setSelectedCategory(defaultCategory);
    setSelectedStatus(defaultStatus);
    setSelectedUserId(defaultUserId);
    setSelectedUserName(defaultUserEmail);
    setTempSelectedCategory(defaultCategory);
    setTempSelectedStatus(defaultStatus);
    setTempSelectedUserId(defaultUserId);
    setTempSelectedUserName(defaultUserEmail);
    setTempCategorySearch('');
    setTempUserSearch('');

    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);

    // When closing without applying, reset temporary states
    if (!open) {
      setTempSelectedCategory(selectedCategory);
      setTempSelectedStatus(selectedStatus);
      setTempSelectedUserId(selectedUserId);
      setTempSelectedUserName(selectedUserName);
      setTempCategorySearch('');
      setTempUserSearch('');
    }
  };

  return {
    // State
    selectedCategory,
    selectedStatus,
    selectedUserId,
    selectedUserName,
    isOpenFilters,
    tempSelectedCategory,
    tempSelectedStatus,
    tempSelectedUserId,
    tempSelectedUserName,
    tempCategorySearch,
    tempUserSearch,

    // Actions
    setSelectedCategory,
    setSelectedStatus,
    setSelectedUserId,
    setSelectedUserName,
    setTempSelectedCategory,
    setTempSelectedStatus,
    setTempSelectedUserId,
    setTempSelectedUserName,
    setTempCategorySearch,
    setTempUserSearch,
    handleApplyFilters,
    handleCancelFilters,
    onClearAll,
    handleFilterOpenChange,
  };
}
