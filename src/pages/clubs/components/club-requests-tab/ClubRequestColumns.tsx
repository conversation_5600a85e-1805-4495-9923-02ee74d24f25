import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Checkbox } from '@/components/ui/Checkbox';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { getCategoryLabel, getUserName } from './ClubRequestColumnHelpers';
import { ClubRequest } from '@/generated/graphql';
import ActionCell from '@/components/ui/table/ActionCell';
import { DATE_FORMAT_MM_DD_YYYY } from '@/lib/constants';

interface ClubRequestColumnsProps {
  isLoading: boolean;
  selectedRows: Record<string, boolean>;
  selectedRowId: string | null;
  actionCellRef?: React.RefObject<HTMLDivElement>;
  showActions: boolean;
  setSelectedRows: (rows: Record<string, boolean>) => void;
  setSelectedRowId: (id: string | null) => void;
  onApproveRequest: (requestId: string) => void;
  onDeclineRequest: (requestId: string) => void;
}

export function generateClubRequestColumns({
  isLoading,
  showActions,
  selectedRowId,
  onApproveRequest,
  onDeclineRequest,
  setSelectedRowId,
}: ClubRequestColumnsProps): ColumnDef<ClubRequest>[] {
  return [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
    },
    {
      accessorKey: 'clubName',
      id: 'clubName',
      header: ({ column }) => <HeaderColumn column={column}>Club Name</HeaderColumn>,
      cell: ({ row }) => {
        const request = row.original;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>
                {request.clubName || 'Unnamed Club'}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
    },
    {
      accessorKey: 'category',
      header: 'Club Category',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {getCategoryLabel(request.category)}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 150,
      size: 200,
    },
    {
      accessorKey: 'clubDescription',
      header: 'Description',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {request.clubDescription || 'No description'}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 300,
    },
    {
      accessorKey: 'user',
      header: 'User',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex items-center gap-3'>
              <Avatar className='w-8 h-8'>
                <AvatarImage src={request.clubProfile?.img?.url ?? ''} />
                <AvatarFallback className='bg-gradient-to-br from-purple-400 to-yellow-400 text-white text-xs font-medium'>
                  {getUserName(request.user?.firstName, request.user?.lastName)
                    .split(' ')
                    .map((n) => n[0])
                    .join('')
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className='flex flex-col'>
                <span className='text-sm font-medium text-gray-900'>
                  {getUserName(request.user?.firstName, request.user?.lastName)}
                </span>
              </div>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
    },
    {
      accessorKey: 'createdAt',
      id: 'createdAt',
      header: ({ column }) => <HeaderColumn column={column}>Requested</HeaderColumn>,
      cell: ({ row }) => {
        const request = row.original;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {request.createdAt
                  ? format(new Date(request.createdAt), DATE_FORMAT_MM_DD_YYYY)
                  : ''}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
    },
    ...(showActions
      ? [
          {
            accessorKey: 'actions',
            header: '',
            cell: ({ row }) => {
              const club = row.original;
              const isSelected = selectedRowId === row.id;

              return (
                <SkeletonCell isLoading={isLoading} skeletonCount={1}>
                  <ActionCell
                    isSelected={isSelected}
                    setSelectedRowId={setSelectedRowId}
                    actions={[
                      {
                        label: 'Approve Request',
                        onClick: () => onApproveRequest(club.id),
                      },
                      {
                        label: 'Decline Request',
                        onClick: () => onDeclineRequest(club.id),
                      },
                    ]}
                  />
                </SkeletonCell>
              );
            },
            minSize: 50,
            size: 50,
          } as ColumnDef<ClubRequest>,
        ]
      : []),
  ];
}
