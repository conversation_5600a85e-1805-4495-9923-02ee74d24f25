import { X } from 'lucide-react';
import { Badge } from '@/components/ui/Badge';
import { ClubCategoryEnum } from '@/generated/graphql';

interface FilterBadgesProps {
  selectedCategory: string;
  selectedStatus: string;
  selectedUserId: string | null;
  selectedUserName?: string | null;
  onClearCategory: () => void;
  onClearStatus: () => void;
  onClearUserId: () => void;
}

export function FilterBadges({
  selectedCategory,
  selectedStatus,
  selectedUserId,
  selectedUserName,
  onClearCategory,
  onClearStatus,
  onClearUserId,
}: FilterBadgesProps) {
  const getCategoryLabel = (categoryValue: string) => {
    const categories = [
      { value: 'All Categories', label: 'All Categories' },
      { value: ClubCategoryEnum.Creative, label: 'Creative' },
      { value: ClubCategoryEnum.FitnessOutdoor, label: 'Fitness & Outdoor' },
      { value: ClubCategoryEnum.FoodDrink, label: 'Food & Drink' },
      { value: ClubCategoryEnum.Hobbies, label: 'Hobbies' },
      { value: ClubCategoryEnum.SocialFamily, label: 'Social & Family' },
    ];
    return categories.find((cat) => cat.value === categoryValue)?.label || categoryValue;
  };

  const getStatusLabel = (statusValue: string) => {
    const statuses = [
      { value: 'All Status', label: 'All Status' },
      { value: 'Pending', label: 'Pending' },
      { value: 'Approved', label: 'Approved' },
      { value: 'Rejected', label: 'Rejected' },
    ];
    return statuses.find((status) => status.value === statusValue)?.label || statusValue;
  };

  const getUserLabel = (userEmail: string | null | undefined) => {
    if (!userEmail) return '';
    return userEmail;
  };

  return (
    <div className='flex flex-col sm:flex-row gap-2'>
      {selectedCategory !== 'All Categories' && (
        <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
          {getCategoryLabel(selectedCategory)}
          <X className='w-4 h-4 cursor-pointer' onClick={onClearCategory} />
        </Badge>
      )}
      {selectedStatus !== 'All Status' && (
        <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
          {getStatusLabel(selectedStatus)}
          <X className='w-4 h-4 cursor-pointer' onClick={onClearStatus} />
        </Badge>
      )}
      {selectedUserId !== null && (
        <Badge className='text-sm gap-2 hover:bg-secondary text-primary w-fit bg-secondary font-medium'>
          {selectedUserName}
          <X className='w-4 h-4 cursor-pointer' onClick={onClearUserId} />
        </Badge>
      )}
    </div>
  );
}
