import { useState } from 'react';
import { useParams } from 'react-router-dom';
import ClubDetailHeader from './components/ClubDetailHeader';
import ClubDetailTabs from './components/ClubDetailTabs';
import MembersTab from './components/members/MembersTab';
import PostsTab from './components/tabs/PostsTab';
import EventsTab from './components/tabs/EventsTab';

const ClubDetail = () => {
  const { clubId, associationId } = useParams<{ clubId: string; associationId: string }>();
  const [activeTab, setActiveTab] = useState<'members' | 'posts' | 'events'>('members');

  if (!clubId || !associationId) {
    return <div>Club not found</div>;
  }

  return (
    <div className='w-full flex py-4 space-y-4 flex-col flex-1 px-4 sm:px-8'>
      <ClubDetailHeader clubId={clubId} />
      <ClubDetailTabs activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Tab Content */}
      {activeTab === 'members' && <MembersTab clubId={clubId} />}
      {activeTab === 'posts' && <PostsTab clubId={clubId} />}
      {activeTab === 'events' && <EventsTab clubId={clubId} />}
    </div>
  );
};

export default ClubDetail;
