import { useState, useCallback } from 'react';
import { ClubMembership } from '@/generated/graphql';

type ClubMember = ClubMembership;

export const useMemberActions = () => {
  const [selectedMember, setSelectedMember] = useState<ClubMember | null>(null);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);

  const handleOpenRemoveMemberModal = useCallback((member: ClubMember) => {
    setSelectedMember(member);
    setIsRemoveModalOpen(true);
  }, []);

  const handleCloseRemoveMemberModal = useCallback(() => {
    setSelectedMember(null);
    setIsRemoveModalOpen(false);
  }, []);

  return {
    selectedMember,
    isRemoveModalOpen,
    handleOpenRemoveMemberModal,
    handleCloseRemoveMemberModal,
  };
};
