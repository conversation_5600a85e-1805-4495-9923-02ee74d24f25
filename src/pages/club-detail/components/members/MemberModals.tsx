import { ClubMembership } from '@/generated/graphql';
import RemoveUserModal from '@/components/modals/confirm-modal/RemoveUserModal';

interface MemberActions {
  selectedMember: ClubMembership | null;
  isRemoveModalOpen: boolean;
  handleOpenRemoveMemberModal: (member: ClubMembership) => void;
  handleCloseRemoveMemberModal: () => void;
}

interface MemberModalsProps {
  memberActions: MemberActions;
  selectedMember: ClubMembership | null;
}

export const MemberModals = ({ memberActions, selectedMember }: MemberModalsProps) => {
  return (
    <>
      {/* Remove Member Modal */}
      <RemoveUserModal
        isOpen={memberActions.isRemoveModalOpen}
        onOpenChange={memberActions.handleCloseRemoveMemberModal}
        onCancel={memberActions.handleCloseRemoveMemberModal}
        onConfirm={() => {
          // Handle remove member confirmation - will be implemented later
          memberActions.handleCloseRemoveMemberModal();
        }}
        isLoading={false}
        title='Remove User'
        description='Are you sure you want to remove this user from this club? Once removed, they will no longer be able to join or post in this club.'
      />
    </>
  );
};
