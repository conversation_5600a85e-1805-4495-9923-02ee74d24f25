import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { ListFilter } from 'lucide-react';
import { useState } from 'react';
import SearchInput from '@/components/ui/search-input/SearchInput';

interface TableHeaderFiltersProps {
  search: string;
  setSearch: (search: string) => void;
}

const TableHeaderFilters = ({ search, setSearch }: TableHeaderFiltersProps) => {
  const [isOpenFilters, setIsOpenFilters] = useState(false);
  return (
    <div className='pb-4'>
      <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4  '>
        <div className='flex w-full items-center gap-2'>
          <SearchInput name='search' onChange={(search) => setSearch(search)} value={search} />
        </div>
        <div className='flex w-full sm:w-auto items-center gap-2'>
          <Popover open={isOpenFilters} onOpenChange={() => {}}>
            <PopoverTrigger asChild>
              <Button
                variant='outline'
                className='gap-2 w-full text-gray-700 font-semibold sm:w-auto'
              >
                <ListFilter className='w-5 h-5' />
                Filters
              </Button>
            </PopoverTrigger>
            <PopoverContent
              align='end'
              className='sm:w-80  w-[--radix-popover-trigger-width]'
            ></PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
};

export default TableHeaderFilters;
