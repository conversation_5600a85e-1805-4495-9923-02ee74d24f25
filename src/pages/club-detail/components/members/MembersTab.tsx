import { useState, useMemo, useCallback } from 'react';
import usePagination from '@/hooks/usePagination';
import useSort from '@/hooks/useSort';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import TableData from '@/components/ui/table/TableData';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { Row } from '@tanstack/react-table';
import { useClubMembersQuery, ClubMembersOrderByEnum, OrderDirection } from '@/generated/graphql';
import { generateMemberColumns } from './MemberColumns';
import { useMemberActions } from '../../hooks/useMemberActions';
import { MemberModals } from './MemberModals';
import TableHeaderCount from '@/components/ui/table/TableHeaderCount';
import TableHeaderFilters from './TableHeaderFilters';

interface MembersTabProps {
  clubId: string;
}

const MembersTab = ({ clubId }: MembersTabProps) => {
  const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({});
  const [columnFilters, setColumnFilters] = useState<any[]>([]);

  const { pagination, setPagination } = usePagination();
  const { sorting, setSorting } = useSort();
  const { searchTemp, setSearchTemp } = useSearchQuery('club-members');

  const memberActions = useMemberActions();
  const { selectedMember } = memberActions;

  const { data: membersData, loading: isLoadingMembers } = useClubMembersQuery({
    variables: {
      clubTemplateId: clubId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        search: searchTemp.trim() || undefined,
      },
      orderBy:
        sorting.length > 0
          ? {
              field: sorting[0]?.id as ClubMembersOrderByEnum,
              direction: sorting[0]?.desc ? OrderDirection.Desc : OrderDirection.Asc,
            }
          : undefined,
    },
  });

  const members = useMemo(() => {
    if (isLoadingMembers) {
      return Array(10).fill({});
    }
    return membersData?.clubMembers?.items ?? [];
  }, [isLoadingMembers, membersData]);

  const totalMembers = useMemo(() => membersData?.clubMembers?.total ?? 0, [membersData]);

  const handleRowClick = useCallback(
    (row: Row<any>) => {
      const newSelectedId = row.id === selectedRowId ? null : row.id;
      setSelectedRowId(newSelectedId);
    },
    [selectedRowId]
  );

  const columns = useMemo(
    () =>
      generateMemberColumns({
        isLoading: isLoadingMembers,
        onRemove: memberActions.handleOpenRemoveMemberModal,
        selectedRowId,
        setSelectedRowId,
      }),
    [isLoadingMembers, memberActions.handleOpenRemoveMemberModal, selectedRowId]
  );

  return (
    <div className='w-full flex flex-col sm:py-8 sm:pb-12 flex-1 rounded-lg'>
      <TableHeaderFilters search={searchTemp} setSearch={setSearchTemp} />
      <div className='w-full border rounded-lg overflow-auto'>
        <TableHeaderCount title='Members' total={totalMembers} />
        <TableData
          columns={columns}
          data={members}
          pagination={pagination}
          sorting={sorting}
          filters={columnFilters}
          onColumnFiltersChange={setColumnFilters}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={setSorting}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
          onRowClick={handleRowClick}
        />
        <TablePagination
          pageCount={Math.ceil(totalMembers / pagination.pageSize)}
          currentPage={pagination.pageIndex - 1}
          onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
        />
      </div>

      <MemberModals memberActions={memberActions} selectedMember={selectedMember} />
    </div>
  );
};

export default MembersTab;
