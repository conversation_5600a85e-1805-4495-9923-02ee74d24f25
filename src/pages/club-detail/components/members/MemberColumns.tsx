import { Checkbox } from '@/components/ui/Checkbox';
import ActionCell from '@/components/ui/table/ActionCell';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { ClubMembership } from '@/generated/graphql';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/lib/constants';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';

type ClubMember = ClubMembership;

interface MemberColumnsProps {
  isLoading: boolean;
  selectedRowId: string | null;
  onRemove: (member: ClubMember) => void;
  setSelectedRowId: (id: string | null) => void;
}

export function generateMemberColumns({
  isLoading,
  selectedRowId,
  onRemove,
  setSelectedRowId,
}: MemberColumnsProps) {
  const columns: ColumnDef<ClubMember>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
    },
    {
      accessorKey: 'id',
      header: ({ column }) => <HeaderColumn column={column}>User ID</HeaderColumn>,
      cell: ({ row }) => {
        const member = row.original;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>
              {member?.id ? member.id.padStart(6, '0') : 'N/A'}
            </span>
          </SkeletonCell>
        );
      },
      minSize: 100,
      size: 120,
    },
    {
      accessorKey: 'displayName',
      header: ({ column }) => <HeaderColumn column={column}>Name</HeaderColumn>,
      cell: ({ row }) => {
        const member = row.original;
        const displayName = member?.clubProfile?.displayName || 'Unknown';
        const firstName = displayName.split(' ')[0] || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex items-center gap-3'>
              <Avatar className='w-8 h-8'>
                <AvatarImage src={member?.clubProfile?.img?.url || undefined} />
                <AvatarFallback className='text-xs'>
                  {firstName.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className='font-medium text-gray-900'>{firstName}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 150,
      size: 200,
      enableSorting: false,
    },
    {
      accessorKey: 'lastName',
      header: ({ column }) => <HeaderColumn column={column}>Last Name</HeaderColumn>,
      cell: ({ row }) => {
        const member = row.original;
        const displayName = member?.clubProfile?.displayName || '';
        const lastName = displayName.split(' ').slice(1).join(' ') || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>{lastName}</span>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      enableSorting: false,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => <HeaderColumn column={column}>Email</HeaderColumn>,
      cell: ({ row }) => {
        const member = row.original;
        const email = 'N/A'; // Email not available in current query

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>{email}</span>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
      enableSorting: false,
    },
    {
      accessorKey: 'joinedAt',
      header: ({ column }) => <HeaderColumn column={column}>Date Joined</HeaderColumn>,
      cell: ({ row }) => {
        const member = row.original;
        const joinedAt = member?.joinedAt;

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>
              {joinedAt ? format(new Date(joinedAt), DATE_FORMAT) : 'N/A'}
            </span>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
    },
    {
      accessorKey: 'actions',
      header: '',
      cell: ({ row }) => {
        const member = row.original;
        const isSelected = selectedRowId === row.id;

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div
              className={cn('flex justify-center', {
                'bg-blue-50': isSelected,
              })}
            >
              <ActionCell
                isSelected={isSelected}
                setSelectedRowId={setSelectedRowId}
                actions={[
                  {
                    label: 'Member User Info',
                    onClick: () => {
                      // Handle view member info - will be implemented later
                    },
                  },
                  {
                    label: 'Remove Member from Club',
                    onClick: () => onRemove(member),
                  },
                ]}
              />
            </div>
          </SkeletonCell>
        );
      },
      minSize: 80,
      maxSize: 80,
    },
  ];
  return columns;
}
