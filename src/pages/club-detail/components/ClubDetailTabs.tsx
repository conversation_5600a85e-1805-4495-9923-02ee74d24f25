interface ClubDetailTabsProps {
  activeTab: 'members' | 'posts' | 'events';
  setActiveTab: (tab: 'members' | 'posts' | 'events') => void;
}

const ClubDetailTabs = ({ activeTab, setActiveTab }: ClubDetailTabsProps) => {
  const tabs = [
    { id: 'members', label: 'Members' },
    { id: 'posts', label: 'Posts' },
    { id: 'events', label: 'Events' },
  ] as const;

  return (
    <div className='w-full bg-white rounded-lg'>
      <div className='border-b border-gray-200'>
        <nav className='flex space-x-8' aria-label='Tabs'>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default ClubDetailTabs;
