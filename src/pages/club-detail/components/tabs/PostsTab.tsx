import { useMemo, useCallback } from 'react';
import { useAdminClubPostsQuery } from '@/generated/graphql';
import usePagination from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Button } from '@/components/ui/Button';
import { ListFilter, MoreVertical, Heart, Flag } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { TablePagination } from '@/components/ui/table/TablePagination';

interface PostsTabProps {
  clubId: string;
}

const PostsTab = ({ clubId }: PostsTabProps) => {
  const { pagination, setPagination } = usePagination();
  const { searchTemp, setSearchTemp } = useSearchQuery('club-posts');

  const { data: postsData, loading: isLoadingPosts } = useAdminClubPostsQuery({
    variables: {
      clubId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
    },
  });

  const posts = useMemo(() => postsData?.adminClubPosts?.items ?? [], [postsData]);
  const totalPosts = useMemo(() => postsData?.adminClubPosts?.total ?? 0, [postsData]);

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchTemp(value);
      // Pagination reset is handled automatically by useSearchQuery hook
    },
    [setSearchTemp]
  );

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d`;
    }
  };

  const getDisplayName = (firstName?: string | null, lastName?: string | null) => {
    if (firstName && lastName) return `${firstName} ${lastName}`;
    if (firstName) return firstName;
    if (lastName) return lastName;
    return 'Unknown User';
  };

  return (
    <div className='w-full bg-white rounded-lg'>
      {/* Search and Filters */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex flex-col sm:flex-row gap-4 items-center justify-between'>
          <div className='flex-1 max-w-md'>
            <SearchInput name='search' onChange={handleSearchChange} value={searchTemp} />
          </div>
          <Button variant='outline' className='gap-2'>
            <ListFilter className='w-4 h-4' />
            Filters
          </Button>
        </div>
      </div>

      {/* Posts Header */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex items-center gap-3'>
          <h3 className='text-lg font-semibold text-gray-900'>Posts</h3>
          <Badge variant='secondary' className='bg-blue-100 text-blue-800'>
            {totalPosts}
          </Badge>
        </div>
      </div>

      {/* Posts List */}
      <div className='divide-y divide-gray-200'>
        {isLoadingPosts ? (
          // Loading skeleton
          Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className='p-6 animate-pulse'>
              <div className='flex items-start gap-4'>
                <div className='w-10 h-10 bg-gray-200 rounded-full'></div>
                <div className='flex-1'>
                  <div className='h-4 bg-gray-200 rounded w-32 mb-2'></div>
                  <div className='h-4 bg-gray-200 rounded w-full mb-2'></div>
                  <div className='h-4 bg-gray-200 rounded w-3/4'></div>
                </div>
              </div>
            </div>
          ))
        ) : posts.length === 0 ? (
          <div className='p-6 text-center text-gray-500'>
            <p>No posts found</p>
          </div>
        ) : (
          posts.map((post) => {
            const hasReports = post.reports && post.reports.length > 0;
            const displayName = getDisplayName(
              post.clubProfile?.user?.firstName,
              post.clubProfile?.user?.lastName
            );

            return (
              <div key={post.id} className={`p-6 ${hasReports ? 'bg-pink-50' : ''}`}>
                <div className='flex items-start gap-4'>
                  {/* Avatar */}
                  <Avatar className='w-10 h-10'>
                    <AvatarImage src={post.clubProfile?.img?.url ?? undefined} />
                    <AvatarFallback className='text-xs'>
                      {displayName.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  {/* Content */}
                  <div className='flex-1 min-w-0'>
                    {/* Header */}
                    <div className='flex items-center gap-2 mb-2'>
                      <span className='font-medium text-gray-900'>{displayName}</span>
                      {post.clubProfile?.user?.role === 'ADMIN' && (
                        <Badge variant='outline' className='text-xs'>
                          Admin
                        </Badge>
                      )}
                      {hasReports && (
                        <div className='flex items-center gap-1'>
                          <Flag className='w-3 h-3 text-red-500' />
                          <Badge variant='destructive' className='text-xs'>
                            {post.reports?.[0]?.category?.title || 'Flagged'}
                          </Badge>
                        </div>
                      )}
                    </div>

                    {/* Post Content */}
                    <div className='text-gray-700 mb-3'>
                      <p className='whitespace-pre-wrap'>{post.content}</p>
                    </div>

                    {/* Report Reason (if flagged) */}
                    {hasReports && post.reports?.[0]?.details && (
                      <div className='mb-3 p-3 bg-red-50 border border-red-200 rounded-md'>
                        <p className='text-sm text-red-700'>
                          <strong>Report reason:</strong> {post.reports[0].details}
                        </p>
                      </div>
                    )}

                    {/* Footer */}
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-4 text-sm text-gray-500'>
                        <span>{formatTimeAgo(post.createdAt || '')}</span>
                        <div className='flex items-center gap-1'>
                          <Heart className='w-4 h-4' />
                          <span>{post.reactionCount || 0}</span>
                        </div>
                      </div>

                      {/* Actions */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                            <MoreVertical className='w-4 h-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          {hasReports && <DropdownMenuItem>Unflag Post</DropdownMenuItem>}
                          <DropdownMenuItem className='text-red-600'>Delete Post</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Pagination */}
      {totalPosts > 0 && (
        <div className='p-6 border-t border-gray-200'>
          <TablePagination
            pageCount={Math.ceil(totalPosts / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        </div>
      )}
    </div>
  );
};

export default PostsTab;
