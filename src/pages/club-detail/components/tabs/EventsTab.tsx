import { useMemo, useCallback } from 'react';
import { useAdminClubEventsQuery } from '@/generated/graphql';
import usePagination from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Button } from '@/components/ui/Button';
import { ListFilter, MoreVertical, Heart, MapPin, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { TablePagination } from '@/components/ui/table/TablePagination';

interface EventsTabProps {
  clubId: string;
}

const EventsTab = ({ clubId }: EventsTabProps) => {
  const { pagination, setPagination } = usePagination();
  const { searchTemp, setSearchTemp } = useSearchQuery('club-events');

  const { data: eventsData, loading: isLoadingEvents } = useAdminClubEventsQuery({
    variables: {
      clubTemplateId: clubId,
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
    },
  });

  const events = useMemo(() => eventsData?.adminClubEvents?.items ?? [], [eventsData]);
  const totalEvents = useMemo(() => eventsData?.adminClubEvents?.total ?? 0, [eventsData]);

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchTemp(value);
      // Pagination reset is handled automatically by useSearchQuery hook
    },
    [setSearchTemp]
  );

  const formatEventTime = (startTime?: string | null, endTime?: string | null) => {
    if (!startTime) return 'TBD';

    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : null;

    const startFormatted = format(start, 'MMMM d, yyyy • h:mm a');
    const endFormatted = end ? format(end, 'h:mm a') : '';

    return endFormatted ? `${startFormatted} - ${endFormatted}` : startFormatted;
  };

  const truncateText = (text: string, maxLength: number = 150) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className='w-full bg-white rounded-lg'>
      {/* Search and Filters */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex flex-col sm:flex-row gap-4 items-center justify-between'>
          <div className='flex-1 max-w-md'>
            <SearchInput name='search' onChange={handleSearchChange} value={searchTemp} />
          </div>
          <Button variant='outline' className='gap-2'>
            <ListFilter className='w-4 h-4' />
            Filters
          </Button>
        </div>
      </div>

      {/* Events Header */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex items-center gap-3'>
          <h3 className='text-lg font-semibold text-gray-900'>Events</h3>
          <Badge variant='secondary' className='bg-blue-100 text-blue-800'>
            {totalEvents}
          </Badge>
        </div>
      </div>

      {/* Events List */}
      <div className='divide-y divide-gray-200'>
        {isLoadingEvents ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className='p-6 animate-pulse'>
              <div className='flex items-start gap-4'>
                <div className='w-10 h-10 bg-gray-200 rounded-full'></div>
                <div className='flex-1'>
                  <div className='h-5 bg-gray-200 rounded w-48 mb-2'></div>
                  <div className='h-4 bg-gray-200 rounded w-32 mb-2'></div>
                  <div className='h-4 bg-gray-200 rounded w-40 mb-2'></div>
                  <div className='h-4 bg-gray-200 rounded w-full mb-2'></div>
                  <div className='h-4 bg-gray-200 rounded w-3/4'></div>
                </div>
              </div>
            </div>
          ))
        ) : events.length === 0 ? (
          <div className='p-6 text-center text-gray-500'>
            <p>No events found</p>
          </div>
        ) : (
          events.map((event) => {
            const displayName = event.clubProfile?.displayName || 'Unknown User';

            return (
              <div key={event.id} className='p-6'>
                <div className='flex items-start gap-4'>
                  {/* Avatar */}
                  <Avatar className='w-10 h-10'>
                    <AvatarImage src={event.clubProfile?.img?.url || undefined} />
                    <AvatarFallback className='text-xs'>
                      {displayName.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  {/* Content */}
                  <div className='flex-1 min-w-0'>
                    {/* Header */}
                    <div className='flex items-center gap-2 mb-3'>
                      <h4 className='text-lg font-semibold text-gray-900'>{event.name}</h4>
                      {/* Removed isNew badge as field was removed from API */}
                    </div>

                    {/* Event Details */}
                    <div className='space-y-2 mb-4'>
                      {/* Time */}
                      <div className='flex items-center gap-2 text-sm text-gray-600'>
                        <Clock className='w-4 h-4' />
                        <span>{formatEventTime(event.startTime, event.endTime)}</span>
                      </div>

                      {/* Location */}
                      {event.location && (
                        <div className='flex items-center gap-2 text-sm text-gray-600'>
                          <MapPin className='w-4 h-4' />
                          <span>{event.location}</span>
                        </div>
                      )}

                      {/* Description */}
                      {event.description && (
                        <div className='text-gray-700'>
                          <p className='whitespace-pre-wrap'>{truncateText(event.description)}</p>
                        </div>
                      )}
                    </div>

                    {/* Footer */}
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-4 text-sm text-gray-500'>
                        <span>{format(new Date(event.createdAt || ''), 'MMM d, yyyy')}</span>
                        <div className='flex items-center gap-1'>
                          <Heart className='w-4 h-4' />
                          <span>{event.reactionCount || 0}</span>
                        </div>
                      </div>

                      {/* Actions */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                            <MoreVertical className='w-4 h-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem>Edit Event</DropdownMenuItem>
                          <DropdownMenuItem className='text-red-600'>Delete Event</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Pagination */}
      {totalEvents > 0 && (
        <div className='p-6 border-t border-gray-200'>
          <TablePagination
            pageCount={Math.ceil(totalEvents / pagination.pageSize)}
            currentPage={pagination.pageIndex - 1}
            onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
          />
        </div>
      )}
    </div>
  );
};

export default EventsTab;
