query AdminClubDetailById($adminClubByIdId: ID!) {
  adminClubById(id: $adminClubByIdId) {
    id
    activatedAt
    memberCount
    clubTemplate {
      id
      name
      description
      about
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      category
      newPost
      hasJoined
      memberCount
    }
  }
}

query ClubMembers($clubTemplateId: ID!, $paginationArgs: PaginationArgs, $filter: ClubMembersFilterInput, $orderBy: ClubMembersOrderInput) {
  clubMembers(clubTemplateId: $clubTemplateId, paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      id
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }

      }
      status
      joinedAt
      deletedAt
    }
    total
    page
    limit
  }
}

query AdminClubPosts($clubId: ID!, $paginationArgs: PaginationArgs) {
  adminClubPosts(clubId: $clubId, paginationArgs: $paginationArgs) {
    items {
      id
      clubId
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
        user {
          id
          firstName
          lastName
          email
          addresses {
            id
            street
            city
            state
            zipCode
            isActive
            isPrimaryUnit
            source
            createdAt
            updatedAt
            isSameAsUnitAddress
          }
          phone
          dob
          birthdayMonth
          birthdayDay
          dateJoined
          association {
            id
            name
          }
          contact {
            id
            salesforceId
          }
          role
          isDeleted
        }
        createdAt
        updatedAt
      }
      content
      isPinned
      reactions {
        id
        postId
        clubProfileId
        createdAt
      }
      reports {
        id
        clubPost {
          id
          clubId
          clubProfile {
            id
            displayName
          }
          content
          isPinned
          reactionCount
          createdAt
          updatedAt
        }
        category {
          id
          code
          title
          description
          ordering
          createdAt
          updatedAt
        }
        reporter {
          id
          displayName
          createdAt
          updatedAt
        }
        status
        details
        createdAt
      }
      reactionCount
      createdAt
      updatedAt
      deletedAt
    }
    total
    page
    limit
  }
}

query AdminClubPostById($postId: ID!) {
  adminClubPostById(postId: $postId) {
    id
    clubId
    clubProfile {
      id
      displayName
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      user {
        id
        firstName
        lastName
        email
        addresses {
          id
          street
          city
          state
          zipCode
          isActive
          isPrimaryUnit
          source
          createdAt
          updatedAt
          isSameAsUnitAddress
        }
        phone
        dob
        birthdayMonth
        birthdayDay
        dateJoined
        association {
          id
          name
        }
        contact {
          id
          salesforceId
        }
        role
        isDeleted
      }
      createdAt
      updatedAt
    }
    content
    isPinned
    reactions {
      id
      postId
      clubProfileId
      createdAt
    }
    reports {
      id
      clubPost {
        id
        clubId
        clubProfile {
          id
          displayName
        }
        content
        isPinned
        reactionCount
        createdAt
        updatedAt
      }
      category {
        id
        code
        title
        description
        ordering
        createdAt
        updatedAt
      }
      reporter {
        id
        displayName
        createdAt
        updatedAt
      }
      status
      details
      createdAt
    }
    reactionCount
    createdAt
    updatedAt
    deletedAt
  }
}

query AdminClubEvents($clubTemplateId: ID!, $paginationArgs: PaginationArgs) {
  adminClubEvents(clubTemplateId: $clubTemplateId, paginationArgs: $paginationArgs) {
    items {
      id
      name
      startTime
      endTime
      location
      description
      reactionCount
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
      }
      createdAt
      updatedAt
    }
    total
    page
    limit
  }
}

query AdminClubEventById($clubEventId: ID!) {
  adminClubEventById(clubEventId: $clubEventId) {
    id
    name
    startTime
    endTime
    location
    description
    reactionCount
    clubProfile {
      id
      displayName
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
    }
    createdAt
    updatedAt
  }
}

# query ClubRequests($paginationArgs: PaginationArgs, $filter: ClubRequestFilterInput, $orderBy: ClubRequestOrderInput) {
#   clubRequests(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
#     items {
#       id
#       clubProfile {
#         id
#         displayName
#         img {
#           id
#           filename
#           key
#           mimeType
#           size
#           status
#           createdAt
#           updatedAt
#           url
#         }
#       }
#       firstName
#       lastName
#       email
#       phone
#       clubName
#       clubDescription
#       category
#       clubAbout
#       status
#       createdAt
#     }
#     total
#     page
#     limit
#   }
# }

query ClubRequestById($clubRequestByIdId: ID!) {
  clubRequestById(id: $clubRequestByIdId) {
    id
    clubProfile {
      id
      displayName
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
    }
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
    }
  }

# mutation UpdateClubRequest($input: UpdateClubRequestInput!) {
#   updateClubRequest(input: $input) {
#     id
#     clubProfile {
#       id
#       displayName
#     }
#     clubName
#     clubDescription
#     category
#     clubAbout
#     status
#     createdAt
#   }
# }