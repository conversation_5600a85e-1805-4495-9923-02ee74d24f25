query AdminAssociations($paginationArgs: PaginationArgs, $filter: AdminAssociationsFilterInput, $orderBy: AdminAssociationsOrderInput) {
  adminAssociations(paginationArgs: $paginationArgs, filter: $filter, orderBy: $orderBy) {
    items {
      id
      name
      memberCount
      clubMemberCount
      createdAt
      updatedAt
      canUseClubs
    }
    total
    page
    limit
  }
}

query AdminAssociationById($adminAssociationByIdId: ID!) {
  adminAssociationById(id: $adminAssociationByIdId) {
    clubMemberCount
    id
    name
    memberCount
    createdAt
    updatedAt
  }
}

mutation toggleAssociationClubFeature($associationId: ID!) {
  toggleAssociationClubFeature(associationId: $associationId) {
    id
    name
    canUseClubs
  }
}