import SearchInput from '@/components/ui/search-input/SearchInput';

interface AssociationTableFilterProps {
  search: string;
  setSearch: (search: string) => void;
}

const AssociationTableFilter = ({ search, setSearch }: AssociationTableFilterProps) => {
  return (
    <div className='pb-4'>
      <div className='w-full gap-2 flex flex-col sm:flex-row items-center justify-between pb-4'>
        <div className='flex w-full items-center gap-2'>
          <SearchInput name='search' onChange={(search) => setSearch(search)} value={search} />
        </div>
      </div>
    </div>
  );
};

export default AssociationTableFilter;
