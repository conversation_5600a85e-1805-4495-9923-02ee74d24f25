import { useState, useCallback } from 'react';
import { AdminAssociation } from '@/generated/graphql';

export const useAssociationActions = () => {
  const [selectedAssociation, setSelectedAssociation] = useState<AdminAssociation | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handleOpenCreateAssociationModal = useCallback(() => {
    setIsCreateModalOpen(true);
  }, []);

  const handleCloseCreateAssociationModal = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);

  const handleOpenEditAssociationModal = useCallback((association: AdminAssociation) => {
    setSelectedAssociation(association);
    setIsEditModalOpen(true);
  }, []);

  const handleCloseEditAssociationModal = useCallback(() => {
    setSelectedAssociation(null);
    setIsEditModalOpen(false);
  }, []);

  const handleOpenDeleteAssociationModal = useCallback((association: AdminAssociation) => {
    setSelectedAssociation(association);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteAssociationModal = useCallback(() => {
    setSelectedAssociation(null);
    setIsDeleteModalOpen(false);
  }, []);

  return {
    selectedAssociation,
    isCreateModalOpen,
    isEditModalOpen,
    isDeleteModalOpen,
    handleOpenCreateAssociationModal,
    handleCloseCreateAssociationModal,
    handleOpenEditAssociationModal,
    handleCloseEditAssociationModal,
    handleOpenDeleteAssociationModal,
    handleCloseDeleteAssociationModal,
  };
};
