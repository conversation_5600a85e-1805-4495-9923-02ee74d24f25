import { Badge } from '../Badge';

interface TableHeaderCountProps {
  title: string;
  total: number;
}

const TableHeaderCount = ({ title, total }: TableHeaderCountProps) => {
  return (
    <div className='p-6 border-b border-gray-200 bg-white'>
      <div className='flex items-center gap-3'>
        <h3 className='text-lg font-medium text-gray-900'>{title}</h3>
        <Badge className='text-xs font-medium text-primary bg-secondary hover:bg-secondary'>
          {total}
        </Badge>
      </div>
    </div>
  );
};

export default TableHeaderCount;
