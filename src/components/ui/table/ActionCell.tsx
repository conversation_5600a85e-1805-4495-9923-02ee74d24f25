import { useCallback, forwardRef } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../DropDownMenu';
import { Button } from '../Button';
import { DotsVerticalIcon } from '@radix-ui/react-icons';

interface Action {
  label: string;
  onClick: () => void;
}

interface ActionCellProps {
  actions: Action[];
  isSelected: boolean;
  setSelectedRowId: (id: string | null) => void;
}

const ActionCell = forwardRef<HTMLDivElement, ActionCellProps>(
  ({ actions, isSelected, setSelectedRowId }, ref) => {
    const handleOpenChange = useCallback(
      (open: boolean) => {
        if (!open) {
          setSelectedRowId(null);
        }
      },
      [setSelectedRowId]
    );

    return (
      <div ref={ref} className='relative'>
        <DropdownMenu modal open={isSelected} onOpenChange={handleOpenChange}>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-5 w-5 p-0'>
              <span className='sr-only'>Open menu</span>
              <DotsVerticalIcon className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='p-0 w-60'>
            {actions?.map((action, index) => (
              <DropdownMenuItem
                key={index}
                className='rounded-none p-2 mt-2 text-gray-700 text-sm font-medium cursor-pointer'
                onClick={(e) => {
                  e.stopPropagation();
                  action.onClick();
                }}
              >
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }
);

ActionCell.displayName = 'ActionCell';

export default ActionCell;
