import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog';
import AlertIcon from '@/assets/images/alert-icon.svg';

interface Props {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  isLoading?: boolean;
}

const RemoveUserModal = ({
  isOpen,
  onOpenChange,
  onCancel,
  onConfirm,
  title,
  description,
  isLoading,
}: Props) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent hideClose className='sm:w-[400px] w-[90vw] rounded-md'>
        <DialogHeader className='flex flex-col gap-2 items-center'>
          <div className='flex items-center gap-2'>
            <img src={AlertIcon} alt='alert' className='w-12 h-12' />
          </div>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription className='text-center'>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className='w-full'>
          <div className='flex flex-1 justify-center gap-2 sm:gap-4 mt-4'>
            <Button className='w-full' variant={'outline'} onClick={onCancel}>
              Cancel
            </Button>
            <Button
              className='w-full'
              variant={'destructive'}
              onClick={onConfirm}
              disabled={isLoading}
              loading={isLoading}
            >
              Delete
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RemoveUserModal;
